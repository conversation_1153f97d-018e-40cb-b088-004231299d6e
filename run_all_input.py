import os
import glob
import subprocess
import argparse

parser = argparse.ArgumentParser(description="Extract information from scientific papers using parallel pipeline")
parser.add_argument("--dir", type=str, default="output", help="Input Output directory")

args = parser.parse_args()

input_dir = args.dir
output_dir = args.dir

md_files = glob.glob(os.path.join(input_dir, '*.md'))

if not md_files:
    print('No .md files found in output loc')
    exit(0)

for md_file in md_files:
    print(f'Processing {md_file}...')
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file,
        '--output-dir',
        output_dir
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
print('Done.')
